# ADS-LAYOUTS tools

The purpose of this library is to provide the tools shared between AdsLayouts microservices.

## Installation

add virtual regitry URL to _.npmrc_ file
[see instructions](https://tvnwbd.atlassian.net/wiki/spaces/SDS/pages/126190896/6.1.+NPM+package+registry+-+artifactory)\
_npm install ads-layouts-tools_

## Setup

**npm run build** - copy files from _src_ to _dist_, minify code, transpile with nest \
**npm run prebuild** - remove _dist_ dir \
**npm run watch** - TypeScript watch \
**npm run test** - run tests with Jest \
**npm run test:watch** - run Jest in watch mode \
**npm run test:cov** - generate coverage report in terminal \
**npm run coverage** - generate coverage reports in _output_ dir (CI requirement)\
**npm run coverage:local** - generate coverage report in _coverage-local_ dir. In order to see visual
report - _cd coverage-local/lcov-report/_, _python -m SimpleHTTPServer_, open localhost:8000 \
**npm publish** - publishes the *dist* dir & readme file to Artifactory [authentication required](https://confluence.tvn.pl/display/SDS/6.1.+NPM+package+registry+-+artifactory)