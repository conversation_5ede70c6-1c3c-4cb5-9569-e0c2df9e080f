{"compilerOptions": {"baseUrl": "./", "outDir": "dist", "module": "NodeNext", "target": "ES2022", "moduleResolution": "nodenext", "allowJs": true, "checkJs": true, "isolatedModules": true, "resolveJsonModule": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "incremental": true, "esModuleInterop": true, "skipLibCheck": true, "lib": ["ES2022"], "strict": true, "noUncheckedIndexedAccess": false, "noImplicitReturns": true, "noImplicitAny": true, "noFallthroughCasesInSwitch": true, "sourceMap": true, "declaration": true, "declarationMap": true, "removeComments": true}, "include": ["libs"]}