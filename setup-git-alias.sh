#!/bin/bash

# Setup script for git post-push hook alias
# This script creates a git alias called 'pushaudit' that uses the post-push hook

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}Setting up git post-push hook alias...${NC}"

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo -e "${YELLOW}Warning: Not in a git repository. The alias will be set globally.${NC}"
    SCOPE="--global"
else
    echo "Setting up alias for this repository only."
    SCOPE=""
fi

# Check if the script exists
if [ ! -f "git-push-with-audit.sh" ]; then
    echo "Error: git-push-with-audit.sh not found in current directory!"
    echo "Please run this script from the repository root where git-push-with-audit.sh is located."
    exit 1
fi

# Get the absolute path to the script
SCRIPT_PATH="$(pwd)/git-push-with-audit.sh"

# Create the git alias
git config $SCOPE alias.pushaudit "!bash \"$SCRIPT_PATH\""

echo -e "${GREEN}✓ Git alias 'pushaudit' has been created!${NC}"
echo ""
echo "Usage examples:"
echo "  git pushaudit                    # Push current branch and run audit"
echo "  git pushaudit origin main        # Push to origin/main and run audit"
echo "  git pushaudit --force-with-lease # Force push with lease and run audit"
echo ""
echo "To remove the alias later, run:"
if [ "$SCOPE" = "--global" ]; then
    echo "  git config --global --unset alias.pushaudit"
else
    echo "  git config --unset alias.pushaudit"
fi
