#!/bin/bash

# Test script for the <PERSON>sky post-push hook implementation
# This script helps verify that the hook is working correctly

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}Testing Husky Post-Push Hook Implementation${NC}"
echo "======================================"
echo

# Check Git version
echo -e "${BLUE}1. Checking Git version...${NC}"
GIT_VERSION=$(git --version)
echo "   $GIT_VERSION"

# Extract version number and check if it's >= 2.24
VERSION_NUM=$(echo "$GIT_VERSION" | grep -oE '[0-9]+\.[0-9]+' | head -1)
MAJOR=$(echo "$VERSION_NUM" | cut -d. -f1)
MINOR=$(echo "$VERSION_NUM" | cut -d. -f2)

if [ "$MAJOR" -gt 2 ] || ([ "$MAJOR" -eq 2 ] && [ "$MINOR" -ge 24 ]); then
    echo -e "   ${GREEN}✓ Git version supports reference-transaction hook${NC}"
else
    echo -e "   ${YELLOW}⚠ Git version may not support reference-transaction hook (requires 2.24+)${NC}"
fi
echo


# Check hook files
echo -e "${BLUE}4. Checking hook files...${NC}"
if [ -f ".husky/reference-transaction" ] && [ -x ".husky/reference-transaction" ]; then
    echo -e "   ${GREEN}✓ reference-transaction hook exists and is executable${NC}"
else
    echo -e "   ${RED}✗ reference-transaction hook missing or not executable${NC}"
    exit 1
fi

if [ -f ".husky/post-push" ] && [ -x ".husky/post-push" ]; then
    echo -e "   ${GREEN}✓ post-push hook exists and is executable${NC}"
else
    echo -e "   ${RED}✗ post-push hook missing or not executable${NC}"
    exit 1
fi
echo

# Check Git hooks symlinks
echo -e "${BLUE}5. Checking Git hooks setup...${NC}"
if [ -L ".git/hooks/reference-transaction" ]; then
    echo -e "   ${GREEN}✓ reference-transaction symlink exists${NC}"
else
    echo -e "   ${YELLOW}⚠ reference-transaction symlink missing - run 'npm run prepare'${NC}"
    exit 1
fi

if [ -L ".git/hooks/post-push" ]; then
    echo -e "   ${GREEN}✓ post-push symlink exists${NC}"
else
    echo -e "   ${YELLOW}⚠ post-push symlink missing - this is expected (Husky handles it)${NC}"
    exit 1
fi
echo

# Check for package.json
echo -e "${BLUE}6. Checking Node.js project setup...${NC}"
if [ -f "package.json" ]; then
    echo -e "   ${GREEN}✓ package.json found${NC}"

    # Check for prepare script
    if grep -q '"prepare".*"husky"' package.json; then
        echo -e "   ${GREEN}✓ prepare script configured for Husky${NC}"
    else
        echo -e "   ${YELLOW}⚠ prepare script not configured${NC}"
    fi
else
    echo -e "   ${YELLOW}⚠ package.json not found - npm audit will be skipped${NC}"
fi

# Check for npm
if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm --version)
    echo -e "   ${GREEN}✓ npm found (version $NPM_VERSION)${NC}"
else
    echo -e "   ${YELLOW}⚠ npm not found - npm audit will be skipped${NC}"
fi
echo

# Check remote configuration
echo -e "${BLUE}7. Checking remote configuration...${NC}"
if git remote get-url origin > /dev/null 2>&1; then
    ORIGIN_URL=$(git remote get-url origin)
    echo -e "   ${GREEN}✓ origin remote configured: $ORIGIN_URL${NC}"
else
    echo -e "   ${YELLOW}⚠ origin remote not configured${NC}"
fi
echo

# Summary
echo -e "${BLUE}Summary:${NC}"
echo "The Husky post-push hook will run automatically when you:"
echo "  • Push to the 'origin' remote"
echo "  • Have a successful push (remote tracking branch gets updated)"
echo "  • Are in a repository with package.json and npm available"
echo
echo "To test the hook:"
echo "  1. Make a small change to a file"
echo "  2. Commit the change: git add . && git commit -m 'test'"
echo "  3. Push to origin: git push"
echo "  4. Look for '[POST-PUSH HOOK]' messages after the push completes"
echo
echo -e "${GREEN}Husky post-push hook setup complete!${NC}"
