# Git Post-Push Hook with <PERSON><PERSON> - "Plug and Play" Setup

This repository implements a true "post-push" hook using <PERSON><PERSON> that runs `npm audit` after a
successful `git push` to inform users about fixable package vulnerabilities.

## 🎯 Key Features

- ✅ **True "Plug and Play"** - No manual `.git/hooks` setup required
- ✅ **Automatic Installation** - Works immediately after `git clone` + `npm install`
- ✅ **Version Controlled** - Hook scripts are part of the repository
- ✅ **Fixable Vulnerability Detection** - Only notifies about actionable issues
- ✅ **Cross-Platform Compatible** - Works on Linux, macOS, and Windows
- ✅ **Non-Intrusive** - Doesn't block pushes, only provides notifications

## 🚀 How It Works

### The "Plug and Play" Magic

1. **Developer clones repository**: `git clone <repo-url>`
2. **Developer runs**: `npm install`
3. **<PERSON><PERSON> automatically sets up hooks** via the `prepare` script
4. **Hooks are ready to use** - no manual configuration needed!

### Technical Implementation

The implementation uses Git's `reference-transaction` hook to detect when remote tracking
branches are updated after a push:

1. **User runs**: `git push origin main`
2. **Git pushes** to remote repository
3. **Git updates** local remote tracking branch `refs/remotes/origin/main`
4. **reference-transaction hook** detects the update
5. **post-push script** is executed, running `npm audit fix --dry-run --json`
6. **Smart notification** only appears if fixable vulnerabilities are found

## 📁 Files Structure

```
.husky/
├── reference-transaction    # Detects remote tracking branch updates
├── post-push               # Runs npm audit and shows notifications
└── _/                      # Husky internal files
```

## 🔧 Installation (Already Done!)

The hooks are already set up and ready to use! Here's what was configured:

### 1. Package.json Configuration

```json
{
  "scripts": {
    "prepare": "husky"
  },
  "devDependencies": {
    "husky": "^9.1.7"
  }
}
```

### 2. Husky Initialization

- Husky was initialized with `npx husky init`
- Hook scripts were created in `.husky/` directory
- Scripts are version-controlled and shared with all team members

## 🎮 Usage

Simply use git push as normal:

```bash
git push
git push origin main
git push --force-with-lease
git push -u origin feature-branch
```

The hook will automatically run `npm audit` after any successful push to the `origin` remote.

## 📋 Sample Output

### When Fixable Vulnerabilities Are Found

```bash
$ git push origin main
Enumerating objects: 5, done.
Counting objects: 100% (5/5), done.
Delta compression using up to 8 threads
Compressing objects: 100% (3/3), done.
Writing objects: 100% (3/3), 324 bytes | 324.00 KiB/s, done.
Total 3 (delta 2), reused 0 (delta 0), pack-reused 0
To github.com:user/repo.git
   abc1234..def5678  main -> main

[POST-PUSH HOOK] Post-push hook triggered - running npm audit...
[POST-PUSH HOOK] Checking for fixable vulnerabilities...

################################################################################
#                           SECURITY ALERT                                    #
################################################################################
# npm audit found fixable vulnerabilities in your project dependencies.       #
#                                                                              #
# Please run 'npm audit fix' locally to resolve these issues.                 #
#                                                                              #
# Review the changes carefully before committing, especially for major updates#
################################################################################

[POST-PUSH HOOK] Summary of fixable actions:
  - update: lodash
  - update: axios

[POST-PUSH HOOK] Post-push hook completed.
```

### When No Fixable Vulnerabilities Are Found

```bash
$ git push origin main
[... git push output ...]

[POST-PUSH HOOK] Post-push hook triggered - running npm audit...
[POST-PUSH HOOK] Checking for fixable vulnerabilities...
[POST-PUSH HOOK] No vulnerabilities found in packages!
[POST-PUSH HOOK] Post-push hook completed.
```

## 🛠 Advanced Configuration

### Customizing Which Remotes/Branches Trigger the Hook

Edit `.husky/reference-transaction` to change which pushes trigger the hook:

```bash
# Current: Only triggers for pushes to 'origin'
if [ "$remote" = "origin" ]

# Example: Trigger for any remote
if [ -n "$remote" ]

# Example: Only trigger for main/master branches
if [ "$remote" = "origin" ] && echo "$branch" | grep -E "^(main|master)$" >/dev/null
```

### Adding Additional Post-Push Actions

Edit `.husky/post-push` to add more commands:

```bash
# Add additional security checks
npm audit
npm outdated
echo "Push completed at $(date)"

# Add notifications (macOS example)
osascript -e 'display notification "Git push completed with audit" with title "Git Hook"'
```

### Installing jq for Better JSON Parsing

For more reliable JSON parsing, install `jq`:

```bash
# macOS
brew install jq

# Ubuntu/Debian
sudo apt-get install jq

# Windows (with Chocolatey)
choco install jq
```

The script works without `jq` but provides better parsing with it installed.

## 🔍 How the "Plug and Play" Setup Works

### 1. The `prepare` Script Magic

The key to the "plug and play" experience is the `prepare` script in `package.json`:

```json
{
  "scripts": {
    "prepare": "husky"
  }
}
```

This script runs automatically:

- After `npm install`
- After `npm ci`
- Before `npm publish`

### 2. Husky's Automatic Hook Installation

When `husky` runs, it:

1. Creates `.git/hooks/` symlinks to `.husky/_/husky.sh`
2. Sets up the hook execution environment
3. Makes all scripts in `.husky/` available as Git hooks

### 3. Version-Controlled Hook Scripts

Unlike traditional Git hooks in `.git/hooks/` (which are not version-controlled), Husky stores
hook scripts in `.husky/` which IS version-controlled. This means:

- All team members get the same hooks
- Hook updates are distributed via git
- No manual setup required for new contributors

## 🧪 Testing Your Setup

### Verify Husky Installation

```bash
# Check if Husky is properly installed
ls -la .git/hooks/
# Should show symlinks to .husky/_/husky.sh

# Check if your hooks exist
ls -la .husky/
# Should show reference-transaction and post-push
```

### Test the Post-Push Hook

```bash
# Make a small change
echo "test" >> README.md
git add README.md
git commit -m "test commit"

# Push and watch for the hook output
git push origin main
```

### Debug Hook Issues

If the hook doesn't run:

1. **Check hook permissions**:

   ```bash
   ls -la .husky/reference-transaction .husky/post-push
   # Both should be executable
   ```

2. **Verify Git version**: Requires Git 2.24+ for reference-transaction hook

   ```bash
   git --version
   ```

3. **Check Husky setup**:
   ```bash
   npm run prepare
   ```

## 🚨 Troubleshooting

### Hook Not Running

- **Ensure you're pushing to `origin`**: The hook only triggers for the `origin` remote
- **Check Git version**: Reference-transaction hook requires Git 2.24+
- **Verify Husky installation**: Run `npm run prepare`

### npm Command Not Found

If you use Node version managers (nvm, volta, asdf), create `~/.config/husky/init.sh`:

```bash
# For nvm users
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
```

### Hook Running Multiple Times

This can happen when pushing multiple branches. The current implementation filters for `origin`
remote only.

## 🔒 Security Considerations

- **Local execution only**: Hooks run with your user permissions
- **No automatic fixes**: The hook only notifies, never automatically modifies code
- **Network dependency**: npm audit requires internet connection
- **Bypassable**: Like all client-side hooks, can be bypassed with `--no-verify`

For production security, complement this with CI/CD pipeline checks that cannot be bypassed.

## 📚 References

- [Husky Documentation](https://typicode.github.io/husky/)
- [npm audit Documentation](https://docs.npmjs.com/cli/v8/commands/npm-audit)
- [Git Hooks Documentation](https://git-scm.com/docs/githooks)
- [Stack Overflow: Post-Push Hook Implementation](https://stackoverflow.com/a/71176054)
