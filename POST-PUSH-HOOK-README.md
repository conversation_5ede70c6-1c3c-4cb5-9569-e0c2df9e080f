# Git Post-Push Hook Implementation

This repository implements a true "post-push" hook that runs `npm audit` after a successful
`git push` to inform users about potential package vulnerabilities.

## Background

Git doesn't provide a native "post-push" hook that runs locally after a successful push. This
implementation uses Git's `reference-transaction` hook to detect when remote tracking branches
are updated after a push, providing true post-push functionality.

**Implementation based on**: [Stack Overflow Answer](https://stackoverflow.com/a/71176054)

## How It Works

The implementation uses Git's `reference-transaction` hook, which is triggered when Git updates
references (including remote tracking branches). After a successful push, Git updates the local
remote tracking branch (e.g., `refs/remotes/origin/main`), which triggers our hook.

### Hook Chain:

1. **User runs**: `git push origin main`
2. **Git pushes** to remote repository
3. **Git updates** local remote tracking branch `refs/remotes/origin/main`
4. **reference-transaction hook** detects the update
5. **post-push hook** is executed, running `npm audit`

## Files

- `.git/hooks/reference-transaction` - Detects remote tracking branch updates
- `.git/hooks/post-push` - The actual post-push script that runs npm audit

## Installation

The hooks are already installed and ready to use! No additional setup required.

### Verification

Run the test script to verify everything is working:

```bash
./test-post-push-hook.sh
```

This will check:

- Git version compatibility
- Hook file permissions
- Node.js/npm availability
- Remote configuration

## Usage

Simply use git push as normal:

```bash
git push
git push origin main
git push --force-with-lease
git push -u origin feature-branch
```

The hook will automatically run `npm audit` after any successful push to the `origin` remote.

## Sample Output

```bash
$ git push origin main
Enumerating objects: 5, done.
Counting objects: 100% (5/5), done.
Delta compression using up to 8 threads
Compressing objects: 100% (3/3), done.
Writing objects: 100% (3/3), 324 bytes | 324.00 KiB/s, done.
Total 3 (delta 2), reused 0 (delta 0), pack-reused 0
To github.com:user/repo.git
   abc1234..def5678  main -> main

[POST-PUSH HOOK] Post-push hook triggered - running npm audit...

==================== NPM AUDIT RESULTS ====================
found 0 vulnerabilities
=============================================================

[POST-PUSH HOOK] No vulnerabilities found in packages!
[POST-PUSH HOOK] Post-push hook completed.
```

## Features

- ✅ **True post-push execution** - Uses Git's native hook system
- ✅ **Automatic activation** - No need to change your workflow
- ✅ **Only runs after successful pushes** - Leverages Git's reference updates
- ✅ **Colored output** for better readability
- ✅ **Graceful handling** when npm or package.json aren't available
- ✅ **Non-intrusive** - Doesn't fail if audit finds vulnerabilities
- ✅ **Repository-specific** - Only affects this repository

## Customization

### Modify Remote or Branch Filtering

Edit `.git/hooks/reference-transaction` to change which pushes trigger the hook:

```bash
# Current: Only triggers for pushes to 'origin'
if [ "$remote" = "origin" ]

# Example: Trigger for any remote
if [[ "$remote" =~ .+ ]]

# Example: Only trigger for main/master branches
if [ "$remote" = "origin" ] && [[ "$branch" =~ ^(main|master)$ ]]
```

### Modify Post-Push Actions

Edit `.git/hooks/post-push` to change what happens after a push:

```bash
# Add additional commands
npm audit
npm outdated
echo "Push completed at $(date)"

# Add notifications (macOS example)
osascript -e 'display notification "Git push completed with audit" with title "Git Hook"'
```

## Technical Details

### Reference Transaction Hook

The `reference-transaction` hook is called whenever Git updates references. It receives three
parameters:

- `$1`: Transaction state (`prepared`, `committed`, or `aborted`)
- stdin: Lines with `oldvalue newvalue refname`

We only act on `committed` transactions for remote tracking branches (`refs/remotes/*`).

### Why This Works

When you push to a remote, Git:

1. Sends objects to the remote repository
2. Updates the remote repository's references
3. Updates your local remote tracking branches (e.g., `origin/main`)
4. The reference update triggers our hook

This ensures the hook only runs after successful pushes.

## Troubleshooting

### Hook Not Running

1. **Check hook permissions**:

   ```bash
   ls -la .git/hooks/reference-transaction .git/hooks/post-push
   # Both should show -rwxr-xr-x (executable)
   ```

2. **Test the reference-transaction hook**:

   ```bash
   # Make a small change and push
   echo "test" >> README.md
   git add README.md
   git commit -m "test commit"
   git push
   ```

3. **Check Git version**: The reference-transaction hook requires Git 2.24+
   ```bash
   git --version
   ```

### Hook Running Multiple Times

If the hook runs multiple times per push, it might be due to pushing multiple branches or tags.
The current implementation filters for `origin` remote only.

### Debugging

Add debug output to `.git/hooks/reference-transaction`:

```bash
# Add after the while loop
echo "DEBUG: Transaction=$1, Remote=$remote, Branch=$branch, Ref=$refname" >> /tmp/git-hook-debug.log
```

## Uninstallation

To remove the post-push hook:

```bash
rm .git/hooks/reference-transaction .git/hooks/post-push
```

## Security Considerations

- Hooks are local to your repository and don't affect other repositories
- The hooks run with your user permissions
- npm audit connects to the npm registry to check for vulnerabilities
- Consider network connectivity when the hook runs
