# Git Post-Push Hook Implementation

This repository includes a custom implementation of a "post-push" hook that runs `npm audit` after a successful `git push` to inform users about potential package vulnerabilities.

## Background

Git doesn't provide a native "post-push" hook that runs locally after a successful push. This implementation provides a workaround using a wrapper script.

## Files

- `git-push-with-audit.sh` - The main wrapper script that executes git push and then runs npm audit

## Usage Options

### Option 1: Direct Script Usage

Use the script directly instead of `git push`:

```bash
# Instead of: git push
./git-push-with-audit.sh

# Instead of: git push origin main
./git-push-with-audit.sh origin main

# Instead of: git push --force-with-lease
./git-push-with-audit.sh --force-with-lease
```

### Option 2: Git Alias (Recommended)

Set up a git alias for convenience:

```bash
# Create a git alias called 'pushaudit'
git config alias.pushaudit '!bash ./git-push-with-audit.sh'

# Now you can use:
git pushaudit
git pushaudit origin main
git pushaudit --force-with-lease
```

### Option 3: Replace git push globally (Advanced)

If you want this to run for ALL git push commands in this repository:

```bash
# Create a local bin directory
mkdir -p ~/bin

# Copy the script to your local bin with the name 'git-push'
cp git-push-with-audit.sh ~/bin/git-push
chmod +x ~/bin/git-push

# Add ~/bin to the beginning of your PATH in your shell profile
# This will make your custom git-push take precedence over the system one
export PATH="~/bin:$PATH"
```

**Warning**: Option 3 affects all git repositories when that PATH is active.

## How It Works

1. **Validation**: Checks if you're in a git repository and if npm/package.json are available
2. **Git Push**: Executes the normal `git push` command with all your arguments
3. **Success Check**: Only proceeds with audit if the push was successful
4. **NPM Audit**: Runs `npm audit` to check for package vulnerabilities
5. **Results**: Shows audit results with colored output for easy reading
6. **Exit Code**: Preserves the original git push exit code

## Features

- ✅ Preserves all `git push` functionality and arguments
- ✅ Only runs audit after successful pushes
- ✅ Colored output for better readability
- ✅ Graceful handling when npm or package.json is not available
- ✅ Doesn't fail the push if audit finds vulnerabilities
- ✅ Informative status messages

## Sample Output

```
[POST-PUSH HOOK] Executing git push...
Enumerating objects: 5, done.
Counting objects: 100% (5/5), done.
Delta compression using up to 8 threads
Compressing objects: 100% (3/3), done.
Writing objects: 100% (3/3), 324 bytes | 324.00 KiB/s, done.
Total 3 (delta 2), reused 0 (delta 0), pack-reused 0
To github.com:user/repo.git
   abc1234..def5678  main -> main

[POST-PUSH HOOK] Git push completed successfully!
[POST-PUSH HOOK] Running npm audit to check for package vulnerabilities...

==================== NPM AUDIT RESULTS ====================
found 0 vulnerabilities
=============================================================

[POST-PUSH HOOK] No vulnerabilities found in packages!
```

## Customization

You can modify `git-push-with-audit.sh` to:
- Run different commands after push (e.g., `npm outdated`, `npm update`, etc.)
- Add additional checks or validations
- Change the output formatting
- Add notifications or logging

## Troubleshooting

- **Script not executable**: Run `chmod +x git-push-with-audit.sh`
- **Command not found**: Use `./git-push-with-audit.sh` or ensure the script is in your PATH
- **Git alias not working**: Make sure you're in the repository root when setting up the alias
