#!/bin/bash

# Reference Transaction Hook - Detects when remote tracking branches are updated after push
# This implements a true post-push hook by monitoring reference transactions
# Based on: https://stackoverflow.com/a/71176054

set -eu

# Read the reference transaction data
while read oldvalue newvalue refname
do
    # Check if this is a committed transaction on a remote tracking branch
    if [ "$1" = "committed" ] && [[ "$refname" =~ ^refs/remotes/.+/.+ ]]
    then
        # Extract remote and branch from refname (e.g., refs/remotes/origin/main)
        remote_and_branch=${refname#refs/remotes/}
        remote=${remote_and_branch%%/*}
        branch=${remote_and_branch#*/}
        
        # Only trigger for pushes to origin (you can modify this condition as needed)
        if [ "$remote" = "origin" ]
        then
            # Execute the post-push hook
            if [ -x ".git/hooks/post-push" ]
            then
                exec .git/hooks/post-push "$remote" "$branch" "$oldvalue" "$newvalue"
            fi
        fi
    fi
done
