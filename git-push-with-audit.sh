#!/bin/bash

# Git Push with Post-Push Audit Hook
# This script acts as a wrapper around 'git push' that runs 'npm audit' after a successful push
# Usage: ./git-push-with-audit.sh [git push arguments]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[POST-PUSH HOOK]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[POST-PUSH HOOK]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[POST-PUSH HOOK]${NC} $1"
}

print_error() {
    echo -e "${RED}[POST-PUSH HOOK]${NC} $1"
}

# Check if we're in a git repository
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    print_error "Not in a git repository!"
    exit 1
fi

# Check if package.json exists (indicating this is a Node.js project)
if [ ! -f "package.json" ]; then
    print_warning "No package.json found. Skipping npm audit."
    exec git push "$@"
fi

# Check if npm is available
if ! command -v npm &> /dev/null; then
    print_warning "npm not found. Skipping npm audit."
    exec git push "$@"
fi

print_status "Executing git push..."

# Execute git push with all provided arguments
# We use a temporary variable to capture the exit code
set +e
git push "$@"
PUSH_EXIT_CODE=$?
set -e

# Check if push was successful
if [ $PUSH_EXIT_CODE -eq 0 ]; then
    print_success "Git push completed successfully!"
    print_status "Running npm audit to check for package vulnerabilities..."
    
    # Run npm audit
    echo ""
    echo "==================== NPM AUDIT RESULTS ===================="
    
    # Run npm audit and capture its exit code, but don't fail the script if audit finds issues
    set +e
    npm audit
    AUDIT_EXIT_CODE=$?
    set -e
    
    echo "============================================================="
    
    if [ $AUDIT_EXIT_CODE -eq 0 ]; then
        print_success "No vulnerabilities found in packages!"
    else
        print_warning "npm audit found potential security issues. Consider running 'npm audit fix' to address them."
        print_status "Note: This doesn't affect your git push, which completed successfully."
    fi
else
    print_error "Git push failed with exit code $PUSH_EXIT_CODE"
fi

# Exit with the same code as git push
exit $PUSH_EXIT_CODE
