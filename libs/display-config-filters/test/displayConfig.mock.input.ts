import { DisplayConfig } from '../../tools';

export const commonMockDisplayConfigData = {
  env: 'default',
  siteVersion: 'default',
  audit: {
    configVersion: 'release/4.45',
    modifiedDate: '2023-12-08 09:56:46',
    generatedBy:
      'https://vafjenadt/job/rc-lambda-ads-layouts----Lambda-Display-Config-Generator/71/'
  }
};

export const ddtvn_configs = [
  {
    ...commonMockDisplayConfigData,
    service: 'ddtvn',
    siteVersion: 'AFTER:2024-05-29T13:00:00',
    release: 'release/1.0.0'
  },
  {
    ...commonMockDisplayConfigData,
    service: 'ddtvn',
    siteVersion: 'AFTER:2023-04-29T15:00:00',
    release: 'release/2.0.0'
  },
  {
    ...commonMockDisplayConfigData,
    service: 'ddtvn',
    siteVersion: 'RELEASE',
    release: 'release/3.0.0'
  },
  {
    ...commonMockDisplayConfigData,
    service: 'ddtvn',
    siteVersion: 'ab_atsdk_ga',
    release: 'release/4.0.0'
  },
  {
    ...commonMockDisplayConfigData,
    service: 'ddtvn',
    siteVersion: 'ab_adph_ga,ab_cwv_gb',
    release: 'release/5.0.0'
  }
];

export const tvn_configs: DisplayConfig[] = [
  {
    ...commonMockDisplayConfigData,
    service: 'tvn',
    release: 'release/6.0.0'
  },
  {
    ...commonMockDisplayConfigData,
    service: 'tvn',
    release: 'release/7.0.0'
  }
];

export const tvn24_configs: DisplayConfig[] = [
  {
    ...commonMockDisplayConfigData,
    service: 'tvn24',
    release: 'release/8.0.0'
  }
];

export const vod_configs = [
  {
    ...commonMockDisplayConfigData,
    service: 'vod',
    siteVersion: 'default',
    release: 'release/9.0.0'
  },
  {
    ...commonMockDisplayConfigData,
    service: 'vod',
    siteVersion: 'A',
    release: 'release/9.0.1'
  },
  {
    ...commonMockDisplayConfigData,
    service: 'vod',
    siteVersion: 'B',
    release: 'release/9.0.2'
  },
  {
    ...commonMockDisplayConfigData,
    service: 'vod',
    siteVersion: 'A,B',
    release: 'release/9.0.3'
  },
  {
    ...commonMockDisplayConfigData,
    service: 'vod',
    siteVersion: 'B,C',
    release: 'release/9.0.4'
  },
  {
    ...commonMockDisplayConfigData,
    service: 'vod',
    siteVersion: 'A,B,C',
    release: 'release/9.0.5'
  },
  {
    ...commonMockDisplayConfigData,
    service: 'vod',
    siteVersion: 'D,E,F',
    release: 'release/9.0.6'
  },
  {
    ...commonMockDisplayConfigData,
    service: 'vod',
    siteVersion: 'D,E,G',
    release: 'release/9.0.7'
  },
  {
    ...commonMockDisplayConfigData,
    service: 'vod',
    siteVersion: 'H',
    release: 'release/9.0.8'
  },
  {
    ...commonMockDisplayConfigData,
    service: 'vod',
    siteVersion: 'I',
    release: 'release/9.0.9'
  }
];

export const mongoTable: DisplayConfig[] = [
  ...ddtvn_configs,
  ...tvn_configs,
  ...tvn24_configs,
  ...vod_configs
];
