import { Test, TestingModule } from '@nestjs/testing';
import { DisplayConfig, ReleaseWithTimestamp } from '../../tools';
import { DisplayConfigFiltersModule, DisplayConfigFiltersService } from '../src';
import { ddtvn_configs, mongoTable, tvn_configs } from './displayConfig.mock.input';

describe('DisplayConfig service test suite', () => {
  let service: DisplayConfigFiltersService;

  beforeAll(async () => {
    const app: TestingModule = await Test.createTestingModule({
      imports: [DisplayConfigFiltersModule.configure(() => jest.fn())]
    }).compile();

    service = app.get(DisplayConfigFiltersService);
  });

  describe('DisplayConfigFiltersService', () => {
    describe('selectReleaseVersion', () => {
      const defaultServiceId = 'ddtvn';
      const defaultEnv = 'default';

      it('should return correct selectedRelease for serviceId', async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: 'tvn',
          serviceEnv: defaultEnv,
          siteVersion: 'default',
          time: '',
          displayConfigs: tvn_configs
        });

        expect(selectedVersion).toEqual('release/6.0.0');
      });

      it('should return default', async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: defaultServiceId,
          serviceEnv: defaultEnv,
          siteVersion: '',
          time: '',
          displayConfigs: ddtvn_configs
        });

        expect(selectedVersion).toEqual('release/3.0.0');
      });

      // AFTER
      it('should select correct value for key AFTER:2024-05-29T13:00:00 for timestamp 29-05-2024 13:00:01', async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: defaultServiceId,
          serviceEnv: defaultEnv,
          siteVersion: '',
          time: '1716980401000', // 29-05-2024 13:00:01
          displayConfigs: ddtvn_configs
        });

        expect(selectedVersion).toEqual('release/1.0.0');
      });

      it('should select correct value for key AFTER:2023-04-29T15:00:00 for timestamp 29-04-2024 15:00:01', async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: defaultServiceId,
          serviceEnv: defaultEnv,
          siteVersion: '',
          time: '1682773201000', // 29-04-2023 15:00:01
          displayConfigs: ddtvn_configs
        });

        expect(selectedVersion).toEqual('release/2.0.0');
      });

      it("should select default value if cannot match 'AFTER' prefixed keys", async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: defaultServiceId,
          serviceEnv: defaultEnv,
          siteVersion: '',
          time: '1682769600000', // 29-04-2023 14:00:00
          displayConfigs: ddtvn_configs
        });

        expect(selectedVersion).toEqual('release/3.0.0');
      });

      // default
      it('should select default value if serviceVersion explicitly equal default', async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: defaultServiceId,
          serviceEnv: defaultEnv,
          siteVersion: 'default',
          time: '1682769600000', // 29-04-2023 14:00:00
          displayConfigs: ddtvn_configs
        });

        expect(selectedVersion).toEqual('release/3.0.0');
      });

      it('should select default value if serviceVersion explicitly equal RELEASE', async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: defaultServiceId,
          serviceEnv: defaultEnv,
          siteVersion: 'RELEASE',
          time: '1682769600000', // 29-04-2023 14:00:00
          displayConfigs: ddtvn_configs
        });

        expect(selectedVersion).toEqual('release/3.0.0');
      });

      it('should select default value if serviceVersion and time not provided', async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: defaultServiceId,
          serviceEnv: defaultEnv,
          siteVersion: '',
          time: '',
          displayConfigs: ddtvn_configs
        });

        expect(selectedVersion).toEqual('release/3.0.0');
      });

      // single
      it('should select correct value for single serviceVersion provided and AFTER key can match', async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: defaultServiceId,
          serviceEnv: defaultEnv,
          siteVersion: 'ab_atsdk_ga',
          time: '1682769600000', // 29-04-2023 14:00:00
          displayConfigs: ddtvn_configs
        });

        expect(selectedVersion).toEqual('release/4.0.0');
      });

      // multiple
      it('should select correct value for multiple serviceVersion provided and AFTER key can match', async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: defaultServiceId,
          serviceEnv: defaultEnv,
          siteVersion: 'ab_adph_ga,ab_cwv_gb',
          time: '1682769600000', // 29-04-2023 14:00:00
          displayConfigs: ddtvn_configs
        });

        expect(selectedVersion).toEqual('release/5.0.0');
      });

      it("should select correct value for multiple serviceVersion that don't match provided and AFTER key can match", async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: defaultServiceId,
          serviceEnv: defaultEnv,
          siteVersion: 'ab_adph_ga,ab_cwv_gb,foo',
          time: '1682769600000', // 29-04-2023 14:00:00
          displayConfigs: ddtvn_configs
        });

        expect(selectedVersion).toEqual('release/5.0.0');
      });

      it("should select correct value for multiple serviceVersion that don't match provided and AFTER key can match", async () => {
        const selectedVersion = await service.selectReleaseVersion({
          serviceId: defaultServiceId,
          serviceEnv: defaultEnv,
          siteVersion: 'ab_adph_ga,foo',
          time: '1682769600000', // 29-04-2023 14:00:00
          displayConfigs: ddtvn_configs
        });

        expect(selectedVersion).toEqual('release/3.0.0');
      });
    });

    describe('getReleaseForSiteVersions', () => {
      let allDisplayConfigsForEnv: DisplayConfig[];
      beforeEach(async () => {
        allDisplayConfigsForEnv = mongoTable;
      });

      describe('should return correct record', () => {
        it('should return first correct record given single specified siteVersion', () => {
          const result = service['getReleaseForSiteVersions'](
            allDisplayConfigsForEnv,
            'default'
          );
          expect(result).toEqual('release/6.0.0');
        });

        it('should return correct record given multiple specified siteVersion', () => {
          const result = service['getReleaseForSiteVersions'](
            allDisplayConfigsForEnv,
            'ab_adph_ga,ab_cwv_gb'
          );
          expect(result).toEqual('release/5.0.0');
        });

        it('should return exact match given single specified siteVersion', () => {
          const result = service['getReleaseForSiteVersions'](allDisplayConfigsForEnv, 'A');
          expect(result).toEqual('release/9.0.1');
        });

        it('should return exact match given 2 keys for specified siteVersion', () => {
          const result = service['getReleaseForSiteVersions'](allDisplayConfigsForEnv, 'A,B');
          expect(result).toEqual('release/9.0.3');
        });

        it('should return exact match given 3 keys for specified siteVersion', () => {
          const result = service['getReleaseForSiteVersions'](
            allDisplayConfigsForEnv,
            'A,B,C'
          );
          expect(result).toEqual('release/9.0.5');
        });

        it('should return correct record without full overlap - 1 key', () => {
          const result = service['getReleaseForSiteVersions'](allDisplayConfigsForEnv, 'A,D');
          expect(result).toEqual('release/9.0.1');
        });

        it('should return correct record without full overlap - 2 keys', () => {
          const result = service['getReleaseForSiteVersions'](
            allDisplayConfigsForEnv,
            'A,B,D'
          );
          expect(result).toEqual('release/9.0.3');
        });

        it('should return first occurrence that match non sufficient siteVersion overlap - 1', () => {
          const result = service['getReleaseForSiteVersions'](allDisplayConfigsForEnv, 'H,I');
          expect(result).toEqual('release/9.0.8');
        });

        it('should return first occurrence that match non sufficient siteVersion overlap - 2', () => {
          const result = service['getReleaseForSiteVersions'](allDisplayConfigsForEnv, 'I,H');
          expect(result).toEqual('release/9.0.8');
        });
      });

      describe('should return undefined', () => {
        it('should return undefined given empty siteVersion', () => {
          const result = service['getReleaseForSiteVersions'](allDisplayConfigsForEnv, '');
          expect(result).toEqual(undefined);
        });

        it('should return undefined given non existent siteVersion', () => {
          const result = service['getReleaseForSiteVersions'](
            allDisplayConfigsForEnv,
            'foo,bar'
          );
          expect(result).toEqual(undefined);
        });

        it('should return undefined non sufficient siteVersion overlap', () => {
          const result = service['getReleaseForSiteVersions'](allDisplayConfigsForEnv, 'D,E');
          expect(result).toEqual(undefined);
        });
      });
    });

    describe('findConfigsWithSiteVersionAFTER', () => {
      let allDisplayConfigsForEnv: DisplayConfig[];
      it('should return records sorted by timeStamp for specified siteVersion', async () => {
        allDisplayConfigsForEnv = mongoTable;
        const result = service['findConfigsWithSiteVersionAFTER'](allDisplayConfigsForEnv);
        const expected: ReleaseWithTimestamp[] = [
          { release: 'release/1.0.0', timestamp: 1716980400000 },
          { release: 'release/2.0.0', timestamp: 1682773200000 }
        ];
        expect(result).toEqual(expected);
      });

      it('should return undefined given if no config match regex pattern', () => {
        allDisplayConfigsForEnv = [];
        const result = service['findConfigsWithSiteVersionAFTER'](allDisplayConfigsForEnv);
        expect(result).toEqual(undefined);
      });
    });

    describe('findConfigsAfterRequestTimestamp', () => {
      let allDisplayConfigsForEnv: DisplayConfig[];
      beforeEach(async () => {
        allDisplayConfigsForEnv = mongoTable; //await mongoDisplayConfigModel.find({});
      });
      it('should return correct release for specified timestamp', () => {
        const result = service['findConfigsAfterRequestTimestamp'](
          allDisplayConfigsForEnv,
          '1700000000000'
        );
        expect(result).toEqual('release/2.0.0');
      });

      it('should return undefined given no config is before timestamp', () => {
        const result = service['findConfigsAfterRequestTimestamp'](
          allDisplayConfigsForEnv,
          '1600000000000'
        );
        expect(result).toEqual(undefined);
      });
    });

    describe('match display config env test suite', () => {
      test('is a function', () => {
        expect(typeof service['matchDisplayConfigEnv']).toEqual('function');
      });

      const data = [
        { env: 'default' },
        { env: 'prev-(1|2|3|4|5|6|7|8)' },
        { env: 'prev-9' }
      ] as DisplayConfig[];

      const dataRepeatedEnv = [
        { env: 'default' },
        { env: 'prev-(1|2|3|4|5|6|7|8)' },
        { env: 'prev-11' },
        { env: 'prev-11' }
      ] as DisplayConfig[];

      test('returns exact match string', () => {
        expect(service['matchDisplayConfigEnv'](data, 'default')).toEqual([
          { env: 'default' }
        ]);
        expect(service['matchDisplayConfigEnv'](data, 'prev-9')).toEqual([{ env: 'prev-9' }]);
      });

      test('returns default if cannot match', () => {
        expect(service['matchDisplayConfigEnv'](data, 'default')).toEqual([
          { env: 'default' }
        ]);
        expect(service['matchDisplayConfigEnv'](data, 'foo')).toEqual([{ env: 'default' }]);
        expect(service['matchDisplayConfigEnv'](data, 'prev-10')).toEqual([
          { env: 'default' }
        ]);
        expect(service['matchDisplayConfigEnv'](data, 'prev-11')).toEqual([
          { env: 'default' }
        ]);
      });

      test('returns matched version', () => {
        expect(service['matchDisplayConfigEnv'](data, 'prev-1')).toEqual([
          { env: 'prev-(1|2|3|4|5|6|7|8)' }
        ]);
        expect(service['matchDisplayConfigEnv'](data, 'prev-4')).toEqual([
          { env: 'prev-(1|2|3|4|5|6|7|8)' }
        ]);
        expect(service['matchDisplayConfigEnv'](data, 'prev-8')).toEqual([
          { env: 'prev-(1|2|3|4|5|6|7|8)' }
        ]);
      });

      test('returns correct object for repeated envs', () => {
        expect(service['matchDisplayConfigEnv'](dataRepeatedEnv, 'prev-11')).toHaveLength(2);
      });

      test('returns default for not provided env', () => {
        expect(service['matchDisplayConfigEnv'](dataRepeatedEnv, 'prev-11')).toHaveLength(2);
      });

      it('should throw error if empty array provided', () => {
        expect(() => service['matchDisplayConfigEnv']([], 'default')).toThrow();
      });
    });
  });
});
