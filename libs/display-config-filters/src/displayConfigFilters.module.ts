import { DynamicModule, Module } from '@nestjs/common';
import { DisplayConfigFiltersService } from './displayConfigFilters.service';
import { LOGGER_TOKEN, LoggerType } from '../../tools';

@Module({})
export class DisplayConfigFiltersModule {
  static configure(logger: LoggerType): DynamicModule {
    return {
      module: DisplayConfigFiltersModule,
      providers: [
        DisplayConfigFiltersService,
        {
          provide: LOGGER_TOKEN,
          useFactory: () => logger
        }
      ],
      exports: [DisplayConfigFiltersService]
    };
  }
}
