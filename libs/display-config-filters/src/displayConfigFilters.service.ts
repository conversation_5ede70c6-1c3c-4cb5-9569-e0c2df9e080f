import { BadRequestException, HttpException, Inject, Injectable } from '@nestjs/common';
import dayjs from 'dayjs';
import { isObject } from 'lodash';
import fetch from 'node-fetch';
import {
  CustomHttpStatus,
  ILambdaDisplayConfig,
  LOGGER_TOKEN,
  LogLevel,
  ReleaseWithTimestamp,
  type DisplayType,
  type LoggerType,
  type selectReleaseVersionArgs
} from '../../tools';

@Injectable()
export class DisplayConfigFiltersService {
  constructor(@Inject(LOGGER_TOKEN) private readonly log: LoggerType) {}

  async getLambdaDisplayConfigs(configUrl: string): Promise<ILambdaDisplayConfig> {
    try {
      this.log('GET_DISPLAY_CONFIG');
      const displayConfigResponse = await fetch(configUrl);

      return (await displayConfigResponse.json()) as ILambdaDisplayConfig;
    } catch (err) {
      this.log('ERROR_GET_DISPLAY_CONFIG', { err }, LogLevel.error);

      const message = err instanceof Error ? err.message : "Can't get display configs";
      throw new BadRequestException(message);
    }
  }

  async parseSDKLambdaDisplayConfigOutput(
    config: ILambdaDisplayConfig
  ): Promise<DisplayType[]> {
    const displayConfigsWithAdConfigsUrls: DisplayType[] = [];
    const { audit, service } = config;

    for (const serviceEl of Object.keys(service)) {
      const envsElements = service[serviceEl].envs;

      for (const envEl of Object.keys(envsElements)) {
        for (const siteVersionEl of Object.keys(envsElements[envEl])) {
          const siteVersionValue = envsElements[envEl][siteVersionEl];

          if (typeof siteVersionValue === 'string') {
            displayConfigsWithAdConfigsUrls.push({
              service: serviceEl,
              env: envEl,
              siteVersion: siteVersionEl,
              release: siteVersionValue,
              audit
            });
          } else if (isObject(siteVersionValue)) {
            for (const siteVersionValueKey of Object.keys(siteVersionValue)) {
              const siteVersionValueEl =
                siteVersionValue[siteVersionValueKey as keyof typeof siteVersionValue];

              if (typeof siteVersionValueEl === 'string') {
                displayConfigsWithAdConfigsUrls.push({
                  service: serviceEl,
                  env: envEl,
                  siteVersion: siteVersionValueKey,
                  release: siteVersionValueEl,
                  audit
                });
              }
            }
          }
        }
      }
    }
    return displayConfigsWithAdConfigsUrls;
  }

  async selectReleaseVersion(args: selectReleaseVersionArgs): Promise<string | undefined> {
    const { serviceId, time, serviceEnv, siteVersion, displayConfigs } = args;

    this.log(
      'SELECT_RELEASE_VERSION_FOR_PARAMS',
      {
        serviceId,
        serviceEnv,
        siteVersion,
        time,
        timeToDate: dayjs(parseInt(time)).format('YYYY-MM-DDTHH:mm:ss:SSSZ')
      },
      LogLevel.dev
    );

    // filter for env
    const allDisplayConfigsForEnv = this.matchDisplayConfigEnv(
      displayConfigs,
      serviceId,
      serviceEnv
    );

    this.log(
      'SELECT_RELEASE_VERSION_MATCHED_ENVS',
      {
        data: allDisplayConfigsForEnv.map(el => ({
          service: el.service,
          env: el.env,
          siteVersion: el.siteVersion,
          release: el.release
        }))
      },
      LogLevel.dev
    );

    const releaseFromSiteVersion = this.getReleaseForSiteVersions(
      allDisplayConfigsForEnv,
      siteVersion
    );
    if (releaseFromSiteVersion) {
      return releaseFromSiteVersion;
    }

    // siteVersion contains AFTER prefix
    if (time) {
      const configsAfterTimestamp = this.findConfigsAfterRequestTimestamp(
        allDisplayConfigsForEnv,
        time
      );
      if (configsAfterTimestamp) {
        return configsAfterTimestamp;
      }
    }

    // empty siteVersion provided
    const defaultConfig = allDisplayConfigsForEnv.find(c =>
      ['default', 'RELEASE'].includes(c.siteVersion)
    );

    this.log(
      'SELECT_RELEASE_VERSION_DEFAULT_SCENARIO',
      { config: defaultConfig },
      LogLevel.dev
    );

    return defaultConfig?.release;
  }

  private getReleaseForSiteVersions(
    displayConfigsForEnv: DisplayType[],
    siteVersion: string | undefined
  ): string | undefined {
    if (!siteVersion) {
      return;
    }

    const isMultipleSiteVersionProvided = siteVersion.includes(',');

    // siteVersion other than empty or default provided
    if (!isMultipleSiteVersionProvided) {
      const selectedSiteVersion = displayConfigsForEnv.find(
        c => c.siteVersion === siteVersion
      );

      if (selectedSiteVersion) {
        this.log(
          'SELECT_RELEASE_VERSION_SINGLE_SITE_VERSION_SCENARIO',
          { selectedSiteVersion },
          LogLevel.dev
        );
        return selectedSiteVersion.release;
      } else {
        return;
      }
    } else {
      const requestSiteVersionSet = new Set(siteVersion.split(','));

      const potentialReleases: Map<number, DisplayType> = new Map();

      for (let i = displayConfigsForEnv.length - 1; i >= 0; i--) {
        const config = displayConfigsForEnv[i];

        let matchingVersionCount = 0;
        const configVersionArr = config.siteVersion.split(',');

        for (const configVersion of configVersionArr) {
          if (requestSiteVersionSet.has(configVersion)) {
            matchingVersionCount++;
          } else {
            matchingVersionCount = -1;
            break;
          }
        }

        if (matchingVersionCount > 0) {
          potentialReleases.set(matchingVersionCount, config);
        }
      }

      if (potentialReleases.size === 0) {
        return;
      }

      const bestMatch = potentialReleases.get(Math.max(...potentialReleases.keys()));
      this.log(
        'SELECT_RELEASE_VERSION_MULTIPLE_SITE_VERSION_SCENARIO',
        { config: bestMatch },
        LogLevel.dev
      );
      return bestMatch!.release;
    }
  }

  private findConfigsWithSiteVersionAFTER(
    allDisplayConfigsForEnv: DisplayType[]
  ): ReleaseWithTimestamp[] | undefined {
    const regexPattern = /^AFTER:(?<afterTimestamp>.+)/;

    const filteredReleasesWithTimestamp = allDisplayConfigsForEnv.reduce(
      (acc: ReleaseWithTimestamp[], curr: DisplayType) => {
        const result = curr.siteVersion.match(regexPattern);
        if (result?.groups) {
          acc.push({
            release: curr.release,
            timestamp: dayjs(result.groups.afterTimestamp).valueOf()
          });
        }
        return acc;
      },
      []
    );

    return filteredReleasesWithTimestamp.length !== 0
      ? filteredReleasesWithTimestamp.sort((a, b) => a.timestamp - b.timestamp).reverse()
      : undefined;
  }
  private findConfigsAfterRequestTimestamp(
    allDisplayConfigsForEnv: DisplayType[],
    time: string
  ): string | undefined {
    const configsSortedByTimestamp =
      this.findConfigsWithSiteVersionAFTER(allDisplayConfigsForEnv);

    const requestIntTimestamp = parseInt(time);
    const configAfterRequestTimestamp = configsSortedByTimestamp?.find(c => {
      this.log(
        'SELECT_RELEASE_VERSION_FIND_CORRECT_AFTER',
        {
          ['AFTER date']: dayjs(c.timestamp).format('YYYY-MM-DDTHH:mm:ss:SSSZ'),
          ['REQUEST date']: dayjs(requestIntTimestamp).format('YYYY-MM-DDTHH:mm:ss:SSSZ'),
          afterTimestamp: c.timestamp,
          requestTimestamp: requestIntTimestamp,
          requestTimestampBiggerThanAfterTimestamp: requestIntTimestamp > c.timestamp
        },
        LogLevel.dev
      );

      return c.timestamp < requestIntTimestamp;
    });

    this.log(
      'SELECT_RELEASE_VERSION_FIND_CORRECT_AFTER_RESULT',
      {
        result: configAfterRequestTimestamp ? 'SUCCESS' : 'FAIL',
        timeStamp: configAfterRequestTimestamp?.timestamp || 'n/a',
        date: dayjs(configAfterRequestTimestamp?.timestamp).format('YYYY-MM-DDTHH:mm:ss:SSSZ'),
        release: configAfterRequestTimestamp?.release || 'n/a'
      },
      LogLevel.dev
    );

    return configAfterRequestTimestamp?.release;
  }

  private matchDisplayConfigEnv(
    displayConfigs: DisplayType[],
    envToMatch = 'default',
    serviceId?: string
  ): DisplayType[] {
    this.log(
      'SELECT_RELEASE_VERSION_MATCH_ENV',
      {
        envToMatch,
        displayConfigs: displayConfigs.map(el => ({
          service: el.service,
          env: el.env,
          siteVersion: el.siteVersion,
          release: el.release
        }))
      },
      LogLevel.info
    );

    if (!displayConfigs?.length) {
      this.log(
        `ERROR_${CustomHttpStatus.CANNOT_FIND_ANY_RELEASE}_CANNOT_FIND_ANY_RELEASE_FOR_ENV`,
        { envToMatch, serviceId, displayConfigs },
        LogLevel.error
      );

      throw new HttpException(
        `Lack of displayConfig for serviceId: ${serviceId}, env: ${envToMatch}`,
        CustomHttpStatus.CANNOT_FIND_ANY_RELEASE
      ); // zamiast CreateException
    }

    const exactMatchFind = displayConfigs.filter(dc => dc.env === envToMatch);

    if (exactMatchFind.length) {
      return exactMatchFind;
    }

    if (envToMatch.includes('-')) {
      const splitedEnvToMatch = envToMatch.split('-');
      const [envPrefix, envVersion] = splitedEnvToMatch;

      const findEnv = displayConfigs.filter(dc =>
        [envPrefix, '-', envVersion].every(el => dc.env.includes(el))
      );

      if (findEnv.length) {
        return findEnv;
      }
    }

    // return default config if cannot match
    return displayConfigs.filter(dc => dc.env === 'default');
  }
}
