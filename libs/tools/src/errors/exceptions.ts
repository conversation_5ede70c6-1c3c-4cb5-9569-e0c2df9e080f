import { HttpException, HttpExceptionOptions, HttpStatus } from '@nestjs/common';
import dayjs from 'dayjs';
import { TracerUtils } from '../logger';
import { CustomHttpStatus, ErrorInterface, LoggerType, LogLevel, ServiceName } from '../types';
import { error2XXhelper, isErrorInterface } from './errorUtils';

export type ExceptionFactoryType = (
  error: HttpException | ErrorInterface | Error,
  options?: HttpExceptionOptions,
  extraLoggerInfo?: string
) => HttpException;

export const configureException = (
  SERVICE_NAME: ServiceName,
  logger: LoggerType,
  tracerUtils: TracerUtils
): ExceptionFactoryType => {
  class CustomException extends HttpException {
    static createKnown(
      message: string,
      statusCode: HttpStatus | CustomHttpStatus,
      options?: HttpExceptionOptions
    ): CustomException {
      return new CustomException(message, statusCode, options);
    }

    static createUnknown(message: string, options?: HttpExceptionOptions): CustomException {
      return new CustomException(
        `Unknown error: ${message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
        options
      );
    }

    private constructor(
      message: string,
      statusCode: HttpStatus | CustomHttpStatus,
      options?: HttpExceptionOptions
    ) {
      super(
        {
          statusCode,
          message,
          dateTime: dayjs().format('YYYY-MM-DDTHH:mm:ss:SSSZ'),
          traceId: tracerUtils.getActiveTraceId()
        },
        statusCode,
        options
      );
    }
  }

  const shouldLogAllErrors = SERVICE_NAME === ServiceName.BACKEND;

  const createException: ExceptionFactoryType = (
    error: CustomException | ErrorInterface | Error,
    options?: HttpExceptionOptions,
    extraLoggerInfo = ''
  ): CustomException => {
    if (error instanceof CustomException) {
      return error;
    }

    if (isErrorInterface(error)) {
      const { message, statusCode } = error;

      const shouldLogError = shouldLogAllErrors || error2XXhelper(statusCode);
      if (shouldLogError) {
        logger(`ERROR_EXCEPTION${extraLoggerInfo}`, { error }, LogLevel.error);
      }

      if (!message) {
        logger(`CRITICAL_ERROR_EXCEPTION${extraLoggerInfo}`, { error }, LogLevel.error);
        return CustomException.createUnknown('Error message is missing', options);
      }

      return CustomException.createKnown(message, statusCode, options);
    }

    if (error instanceof Error) {
      logger(`ERROR_EXCEPTION${extraLoggerInfo}`, { error }, LogLevel.error);
      return CustomException.createUnknown(error.message, options);
    }

    logger(`CRITICAL_ERROR_EXCEPTION${extraLoggerInfo}`, { error }, LogLevel.error);
    return CustomException.createUnknown('This error should never happen', options);
  };

  return createException;
};
