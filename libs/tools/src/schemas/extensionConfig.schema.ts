import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { HydratedDocument } from 'mongoose';
import type {
  ExtensionConfigType,
  IExtensionAudit,
  IExtensionEnable,
  IExtensionSchedule
} from '../types';

export type ExtensionConfigDocument = HydratedDocument<ExtensionConfig>;

@Schema({
  timestamps: true,
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      delete ret.id;
      return ret;
    }
  }
})
export class ExtensionConfig implements ExtensionConfigType {
  @Prop({ type: Number })
  timestamp!: number;

  @Prop({ type: mongoose.Schema.Types.Mixed })
  audit!: IExtensionAudit;

  @Prop({ type: mongoose.Schema.Types.Mixed })
  superPanelEnabled?: IExtensionEnable;

  @Prop({ type: mongoose.Schema.Types.Mixed })
  topPremiumEnabled?: IExtensionEnable;

  @Prop({ type: mongoose.Schema.Types.Mixed })
  superPanelSchedule?: IExtensionSchedule;

  @Prop({ type: mongoose.Schema.Types.Mixed })
  topPremiumSchedule?: IExtensionSchedule;
}

export const ExtensionConfigSchema = SchemaFactory.createForClass(ExtensionConfig);
