import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { IAdConfigAuditData } from '../../types';

@Schema({
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      delete ret.id;
      return ret;
    }
  }
})
export class AdConfigAuditData implements IAdConfigAuditData {
  @Prop({ type: String })
  releaseVersion!: string;

  @Prop({ type: String })
  modifiedDate!: string;

  @Prop({ type: String })
  generatedBy!: string;
}

export const AdConfigAuditDataSchema = SchemaFactory.createForClass(AdConfigAuditData);
