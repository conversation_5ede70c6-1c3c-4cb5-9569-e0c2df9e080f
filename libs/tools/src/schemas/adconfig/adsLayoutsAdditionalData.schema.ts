import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { IAdsLayoutsAdditionalData } from '../../types';

@Schema({
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      delete ret.id;
      return ret;
    }
  }
})
export class AdsLayoutsAdditionalData implements IAdsLayoutsAdditionalData {
  @Prop({ type: URL })
  releaseUrl!: URL;

  @Prop({ type: String })
  releaseName!: string;

  @Prop({ type: [String] })
  releaseServices!: string[];
}

export const AdsLayoutsAdditionalDataSchema = SchemaFactory.createForClass(
  AdsLayoutsAdditionalData
);
