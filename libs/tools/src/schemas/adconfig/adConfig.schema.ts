import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { HydratedDocument } from 'mongoose';
import { IAdConfig } from '../../types';
import { AdConfigAuditData, AdConfigAuditDataSchema } from './adConfig.auditData.schema';
import {
  AdsLayoutsAdditionalData,
  AdsLayoutsAdditionalDataSchema
} from './adsLayoutsAdditionalData.schema';
import { PlaceholdersConfig, PlaceholdersConfigSchema } from './placeholdersConfig.schema';

export type AdConfigDocument = HydratedDocument<AdConfig>;

@Schema({
  timestamps: true,
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      delete ret.id;
      return ret;
    }
  }
})
export class AdConfig implements IAdConfig {
  @Prop({
    type: AdsLayoutsAdditionalDataSchema,
    schema: AdsLayoutsAdditionalDataSchema
  })
  adsLayoutsAdditionalData!: AdsLayoutsAdditionalData;

  @Prop({
    type: AdConfigAuditDataSchema,
    schema: AdConfigAuditDataSchema
  })
  auditData!: AdConfigAuditData;

  @Prop({ type: String })
  config_name!: string;

  @Prop({ type: String })
  src!: string;

  @Prop({ type: [String] })
  pageType!: string[];

  @Prop({ type: [String] })
  serviceId!: string[];

  @Prop({ type: [String] })
  pageId!: string[];

  @Prop({ type: mongoose.Schema.Types.Mixed })
  section!: { id: string; name: string }[];

  @Prop({
    type: PlaceholdersConfigSchema,
    schema: PlaceholdersConfigSchema
  })
  config!: PlaceholdersConfig;
}

export const AdConfigSchema = SchemaFactory.createForClass(AdConfig).index(
  {
    'adsLayoutsAdditionalData.releaseServices': 1,
    'adsLayoutsAdditionalData.releaseName': 1
  },
  { name: 'releaseIndex' }
);
