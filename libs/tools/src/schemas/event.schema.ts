import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { HydratedDocument } from 'mongoose';
import {
  type AdConfigOverride,
  elementsPositionsType,
  EventTypeEnum,
  IEvent,
  IExcludeFacts,
  IParams,
  IPlaceholder,
  lastPlaceholderPosition,
  PlaceholderEnum,
  PlaceholderPositionEnum
} from '../types';
import { is } from '../utils';

@Schema({
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      delete ret.id;
      return ret;
    }
  }
})
export class ExcludeFactsModel implements IExcludeFacts {
  @Prop({ type: [String], required: true })
  omittedFactNames!: string[];

  @Prop({ type: [String], required: true })
  skipOverFactNames!: string[];
}

const ExcludeFactsSchema = SchemaFactory.createForClass(ExcludeFactsModel);

@Schema({
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      delete ret.id;
      return ret;
    }
  }
})
export class PlaceholderModel implements IPlaceholder {
  @Prop({ type: String, enum: PlaceholderPositionEnum })
  position?: PlaceholderPositionEnum;

  @Prop({ type: [String], default: undefined, isRequired: false })
  element?: string[]; // fact names

  @Prop({
    type: Array<number | lastPlaceholderPosition>,
    default: undefined,
    validate: (val: (number | lastPlaceholderPosition)[]) =>
      val?.length ? val.every(v => Number.isInteger(v) || is.string(v)) : true
  })
  placeholderPositions?: Array<number | lastPlaceholderPosition>;

  @Prop({ type: String, default: undefined, enum: PlaceholderEnum })
  placeholderType?: PlaceholderEnum;

  @Prop({
    type: Boolean,
    default: undefined
  })
  countBackwards?: boolean;

  @Prop({
    type: [Number],
    default: undefined,
    validate: (val: number[]) => (val?.length ? val.every(v => Number.isInteger(v)) : true) // TODO: refactor with required field
  })
  elementsIndexes?: number[];

  @Prop({
    type: Array,
    default: undefined,
    validate: {
      validator: (val: elementsPositionsType[]): boolean =>
        val?.length
          ? val.every(
              v =>
                Array.isArray(v) &&
                v.length === 2 &&
                Number.isInteger(v[0]) &&
                [PlaceholderPositionEnum.ABOVE, PlaceholderPositionEnum.UNDER].includes(v[1])
            )
          : true,
      message: (props: any) =>
        `${props.value} is not a elementsPositions value! Please use [[<INT>, "above"], [<INT>, "under"]] format`
    }
  })
  elementsPositions?: elementsPositionsType[];

  @Prop({ type: [Number], default: undefined })
  elementIndex?: number[];

  @Prop({ type: Number })
  every?: number;

  @Prop({
    type: Number,
    validate: {
      validator: (value: unknown) => Number.isInteger(value) && (value as number) > 0,
      message: (props: any) => `${props.value} has to be integer greater than zero`
    }
  })
  max?: number;

  @Prop({ type: Boolean })
  ommitLast?: boolean;

  @Prop({ type: Number })
  startIndex?: number;

  @Prop({
    type: ExcludeFactsSchema,
    schema: ExcludeFactsSchema,
    required: false,
    validate: {
      validator: (obj: ExcludeFactsModel) => {
        if (obj.omittedFactNames.length === 0 && obj.skipOverFactNames.length === 0) {
          throw new Error(
            'If no omittedFactNames or skipOverFactNames are provided, it is better not to define exclude'
          );
        }
      },
      message: (props: any) => `Invalid value: ${props.value}`
    }
  })
  exclude?: ExcludeFactsModel;

  @Prop({
    type: Boolean,
    required: false,
    default: undefined
  })
  enableVariant?: boolean;
}

const PlaceholderSchema = SchemaFactory.createForClass(PlaceholderModel);

@Schema({
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      delete ret.id;
      return ret;
    }
  }
})
export class ParamsModel implements IParams {
  @Prop({ type: [String] })
  fact!: string[];

  @Prop({ type: Number })
  factPosition?: number;

  @Prop({ type: String })
  parentFact?: string;

  @Prop({ type: [String] })
  searchInFacts!: string[];

  @Prop({ type: [String] })
  excludeFromSearch!: string[];

  @Prop({ type: String })
  containsSomeType?: string;

  @Prop({
    type: String,
    required: true
  })
  adConfigGroup!: string;

  @Prop({ type: mongoose.Schema.Types.Mixed })
  adConfigOverride?: AdConfigOverride;

  @Prop({ type: Number })
  priority?: number;

  @Prop({ type: String })
  priorityGroup?: string;

  @Prop({
    type: PlaceholderSchema,
    schema: PlaceholderSchema
  })
  placeholder!: PlaceholderModel;
}

const ParamsSchema = SchemaFactory.createForClass(ParamsModel);

ParamsSchema.pre('validate', function (next) {
  const hasParentFact = this.parentFact !== undefined && this.parentFact !== null;
  const hasFactPosition = this.factPosition !== undefined && this.factPosition !== null;

  const valid = (hasParentFact && hasFactPosition) || (!hasParentFact && !hasFactPosition);

  if (!valid) {
    return next(
      new Error(
        `Both 'parentFact' and 'factPosition' must be provided together or omitted entirely.`
      )
    );
  }

  next();
});

@Schema({
  timestamps: true,
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret.id;
      return ret;
    }
  }
})
export class Event implements IEvent {
  @Prop({
    type: String,
    required: true,
    trim: true,
    validate: {
      validator: (v: string) => /^[A-Za-z0-9]*$/.test(v),
      message: (props: any) =>
        `${props.value} is not a valid event name! Please use letters and digits only.`
    }
  })
  name!: string;

  @Prop({ type: String, required: true })
  desc!: string;

  @Prop({
    type: String,
    required: true,
    enum: EventTypeEnum
  })
  type!: EventTypeEnum;

  @Prop({
    required: true,
    type: ParamsSchema,
    schema: ParamsSchema
  })
  params!: ParamsModel;

  @Prop({ type: String, required: false })
  rulesPackage?: string;
}

export const EventSchema = SchemaFactory.createForClass(Event);
export type EventDocument = HydratedDocument<Event>;
