import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type ServiceToPackageMapDocument = HydratedDocument<ServiceToPackageMap>;

@Schema({
  timestamps: true,
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      delete ret.id;
      return ret;
    }
  }
})
export class ServiceToPackageMap {
  @Prop({
    type: [String],
    required: true,
    validate: {
      validator: (value: string[]) => new Set(value).size === value.length,
      message: (props: any) => `${props.value} has duplicated values!`
    }
  })
  serviceId!: string[];

  @Prop({
    type: String,
    required: true
  })
  rulesPackage!: string;
}

export const ServiceToPackageMapSchema = SchemaFactory.createForClass(ServiceToPackageMap);
