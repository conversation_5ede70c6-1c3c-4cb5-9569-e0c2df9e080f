import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { HydratedDocument } from 'mongoose';
import {
  AdConfigDeviceTypeEnum,
  type BodyElementsModules,
  type WeightsConfig
} from '../types';

@Schema({
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      delete ret.id;
      return ret;
    }
  }
})
export class VariantDeviceTypes implements WeightsConfig {
  @Prop({ type: mongoose.Schema.Types.Mixed })
  [AdConfigDeviceTypeEnum.DESKTOP]?: BodyElementsModules;

  @Prop({ type: mongoose.Schema.Types.Mixed })
  [AdConfigDeviceTypeEnum.TABLET]?: BodyElementsModules;

  @Prop({ type: mongoose.Schema.Types.Mixed })
  [AdConfigDeviceTypeEnum.SMARTPHONE]?: BodyElementsModules;
}

export const VariantDeviceTypesSchema = SchemaFactory.createForClass(VariantDeviceTypes);

@Schema({
  timestamps: true,
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      delete ret.id;
      return ret;
    }
  }
})
export class VariantWeightsConfig {
  @Prop({ type: Number, required: true })
  timestamp!: number;

  @Prop({ type: String, required: true })
  formattedDate!: string;

  @Prop({
    type: VariantDeviceTypesSchema,
    schema: VariantDeviceTypesSchema,
    required: true
  })
  weights!: VariantDeviceTypes;
}

export const VariantWeightsConfigSchema = SchemaFactory.createForClass(VariantWeightsConfig);

export type VariantWeightsConfigDocument = HydratedDocument<VariantWeightsConfig>;
