import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type RulesPackagesDocument = HydratedDocument<RulesPackages>;

@Schema({
  timestamps: true,
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      delete ret.id;
      return ret;
    }
  }
})
export class RulesPackages {
  @Prop({ type: String, unique: true, required: true })
  name!: string;

  @Prop({ type: String })
  version?: string;

  @Prop({ type: Boolean })
  freeze?: boolean;

  @Prop({ type: [String], required: false })
  rules?: string[];
}

export const RulesPackagesSchema = SchemaFactory.createForClass(RulesPackages);
