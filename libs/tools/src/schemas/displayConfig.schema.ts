import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import {
  DisplayConfigAuditData,
  DisplayConfigAuditDataSchema
} from './displayConfig.auditData.schema';
import { DisplayType } from '../types';

export type DisplayConfigDocument = HydratedDocument<DisplayConfig>;

@Schema({
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      delete ret.id;
      return ret;
    }
  }
})
export class DisplayConfig implements DisplayType {
  @Prop({
    type: String,
    required: true
  })
  service!: string;

  @Prop({
    type: String,
    required: true
  })
  env!: string;

  @Prop({
    type: String,
    required: true
  })
  siteVersion!: string;

  @Prop({
    type: String,
    required: true
  })
  release!: string;

  @Prop({ type: String })
  releaseUrl?: string; // BE

  @Prop({
    type: DisplayConfigAuditDataSchema,
    schema: DisplayConfigAuditDataSchema,
    required: true
  })
  audit!: DisplayConfigAuditData;
}

export const DisplayConfigSchema = SchemaFactory.createForClass(DisplayConfig);
