import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { CachePartsEnum } from '../types';

export type WorkersCacheConfigDocument = HydratedDocument<WorkersCacheConfig>;

@Schema({
  timestamps: true,
  toJSON: {
    virtuals: true,
    versionKey: false,
    transform(doc: any, ret: any) {
      delete ret._id;
      delete ret.id;
      return ret;
    }
  }
})
export class WorkersCacheConfig {
  @Prop({ type: Number, required: true })
  timestamp!: number;

  @Prop({ type: String, required: true })
  formatedDate!: string;

  @Prop({ type: String, required: true })
  serviceId!: string;

  @Prop({ type: String, required: true, enum: CachePartsEnum })
  cachePart!: CachePartsEnum;
}

export const WorkersCacheConfigSchema = SchemaFactory.createForClass(WorkersCacheConfig);
