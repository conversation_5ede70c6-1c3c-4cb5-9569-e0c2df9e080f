/**
 * Type utility that prettifies an object type by simplifying its structure.
 *
 * The resulting type will be displayed as if you had written out its
 * properties manually, with their respective types and optional markers.
 *
 * @example
 * type ExtensionConfigType = Prettify<
 *   { timestamp: number; audit: IExtensionAudit } &
 *   Partial<Record<ExtEnableDataEnum, IExtensionEnable>> &
 *   Partial<Record<ExtScheduleDataEnum, IExtensionSchedule>>
 * >;
 *
 * // Resulting type when you hover over `ExtensionConfigType`:
 * type ExtensionConfigType = {
 *   timestamp: number;
 *   audit: IExtensionAudit;
 *   superPanelEnabled?: IExtensionEnable;
 *   topPremiumEnabled?: IExtensionEnable;
 *   superPanelSchedule?: IExtensionSchedule;
 *   topPremiumSchedule?: IExtensionSchedule;
 * }
 */
export type Prettify<T> = {
  [K in keyof T]: T[K];
} & {};

export enum ServiceName {
  BACKEND = 'adslayoutsbackend',
  WORKER = 'adslayoutsworker'
}
