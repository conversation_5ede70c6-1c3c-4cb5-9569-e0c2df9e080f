export type DisplayType = {
  service: string;
  env: string;
  siteVersion: string;
  release: string;
  audit: DisplayConfigAuditDataType;
};

export type DisplayConfigAuditDataType = {
  configVersion: string;
  modifiedDate: string;
  generatedBy: string;
};

type IServiceEnv = Record<string, any>;

type IServiceId = { file: string; envs: IServiceEnv };

type IService = Record<string, IServiceId>;

export interface ILambdaDisplayConfig {
  prefix: string;
  service: IService;
  'x-at-env': string;
  'cache-control': string;
  audit: DisplayConfigAuditDataType;
}

export interface ReleaseWithTimestamp {
  release: string;
  timestamp: number;
}

export interface selectReleaseVersionArgs {
  serviceId: string;
  serviceEnv?: string;
  siteVersion?: string;
  time: string;
  displayConfigs: DisplayType[];
}
