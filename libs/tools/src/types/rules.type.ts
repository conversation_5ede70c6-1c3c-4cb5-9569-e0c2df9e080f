import { IConditions, IEvent } from '../types';

export enum AccessModelEnum {
  AVOD = 'avod',
  SAVOD = 'savod',
  SVOD = 'svod'
}

export enum PaywallEnum {
  YES = 'yes',
  NO = 'no'
}

export interface IRule {
  name: string;
  pageType: string[];
  locationInfoPageType?: string[];
  serviceId: string[];
  layout: string[];
  enabled: boolean;
  sectionId?: string[];
  pageId?: string[];
  conditionsName: string;
  conditions: IConditions;
  eventName: string;
  event: IEvent;
  priority?: number;
  rulesPackage?: string;
  siteVersion?: string[];
  paywall?: PaywallEnum[];
  accessModel?: AccessModelEnum[];
}
