export enum ConditionRuleEnum {
  EQUAL = 'equal',
  LESS_THAN = 'lessThan',
  LESS_OR_EQUAL_TO = 'lessOrEqualTo',
  MORE_THAN = 'moreThan',
  MORE_OR_EQUAL_TO = 'moreOrEqualTo'
}

/*
Tightly coupled with classes AllConditions and AnyConditions
*/
export enum ConditionsKindNamesEnum {
  All = 'AllConditions',
  Any = 'AnyConditions'
}

export interface IFlatConditionValue {
  type?: string;
  count?: number;
  expectedFact?: string;
  position?: number;
  excludedFacts?: string[];
  expectOneFactOnly?: boolean;
  rule?: ConditionRuleEnum;
}

export interface IFlatConditionProperties {
  fact: string;
  operator: OperatorEnum;
  value: IFlatConditionValue;
}

export interface IConditions {
  name: string;
  rulesPackage?: string;
  kind: ConditionsKindNamesEnum;
}

export interface IAllConditions extends IConditions {
  all: IFlatConditionProperties[];
  kind: ConditionsKindNamesEnum.All;
}

export interface IAnyConditions extends IConditions {
  any: IFlatConditionProperties[];
  kind: ConditionsKindNamesEnum.Any;
}

export enum OperatorEnum {
  CONTAINS_OF_TYPE = 'containsOfType',
  EXPECTED_POSITION = 'expectedPosition'
}
