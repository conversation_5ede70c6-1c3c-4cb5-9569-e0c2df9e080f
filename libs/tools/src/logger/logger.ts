import {
  ErrorCheckResult,
  LogColors,
  LogDataType,
  LoggerData,
  LoggerType,
  LogLevel,
  RepoEnv,
  ServiceEnvEnum,
  ClusterID
} from '../types';
import * as nodeColorLog from 'node-color-log';
import * as dayjs from 'dayjs';
import { TracerUtils } from './trace';

export const LOGGER_TOKEN = 'LOGGER_TOKEN';

abstract class ILogger {
  abstract executeDataLog(args: LoggerData): void;
}

class LoggerUtils {
  static stringifyLogData(data?: object): object {
    if (!data) {
      return {};
    }
    return JSON.parse(
      JSON.stringify(data, (k, v) => {
        if (v === undefined) {
          return 'undefined';
        }
        if (v === null) {
          return 'null';
        }
        return v;
      })
    );
  }

  static isWithError(logData?: LogDataType): ErrorCheckResult {
    if (logData) {
      for (const property of Object.values(logData)) {
        if (property instanceof Error) {
          return { hasErrInstance: true, error: property };
        }
      }
    }
    return { hasErrInstance: false, error: undefined };
  }

  static getErrorDetails(error: Error) {
    const { name, message, stack } = error;
    return { errName: name, errMsg: message, errStack: stack };
  }
}

class WorkerLocalLogger implements ILogger {
  constructor(
    private logger: typeof nodeColorLog,
    private clusterID?: ClusterID
  ) {}

  getLoggingColors(logLevel: LogLevel): { logTextColor: LogColors; logBgColor: LogColors } {
    let logTextColor = LogColors.white;
    let logBgColor = LogColors.black;

    switch (logLevel) {
      case LogLevel.error:
        logTextColor = LogColors.white;
        logBgColor = LogColors.red;
        break;

      case LogLevel.warn:
        logTextColor = LogColors.white;
        logBgColor = LogColors.yellow;
        break;

      case LogLevel.info:
      case undefined:
        logTextColor = LogColors.green;
        logBgColor = LogColors.black;
        break;

      case LogLevel.cache:
        logTextColor = LogColors.cyan;
        break;

      case LogLevel.dev:
        logTextColor = LogColors.magenta;
        break;

      case LogLevel.external:
        logTextColor = LogColors.blue;
        break;
    }

    return { logTextColor, logBgColor };
  }

  getFormattedDataTime() {
    return dayjs.default().format('YYYY-MM-DDTHH:mm:ss:SSSZ');
  }

  loggerCommon = (args: LoggerData) => {
    const { message, logLevel } = args;
    const { logTextColor, logBgColor } = this.getLoggingColors(logLevel);

    let loggerInstance = this.logger
      .dim()
      .log(this.getFormattedDataTime())
      .joint()
      .color(logTextColor)
      .bgColor(logBgColor)
      .bold()
      .log(` | ${message} | `)
      .joint();

    if (this.clusterID) {
      loggerInstance = loggerInstance.log(`${this.clusterID} | `).joint();
    }

    loggerInstance = loggerInstance.log(`${logLevel} | `);

    return loggerInstance;
  };

  executeDataLog(args: LoggerData) {
    const { logData } = args;

    const { hasErrInstance, error } = LoggerUtils.isWithError(logData);

    if (hasErrInstance) {
      console.log('!'.repeat(1000));
      console.trace(error);
      console.log('!'.repeat(1000));
    }

    const loggerInstance = this.loggerCommon(args);

    if (logData) {
      loggerInstance
        .joint()
        .log(
          LoggerUtils.stringifyLogData(
            hasErrInstance ? LoggerUtils.getErrorDetails(error) : logData
          )
        );
    }
  }
}

class WorkerDataDogLogger implements ILogger {
  constructor(
    private serviceName: string,
    private appEnv: ServiceEnvEnum,
    private tracerUtils: TracerUtils,
    private clusterID?: ClusterID
  ) {}

  executeDataLog(args: LoggerData) {
    const { message, logData, logLevel } = args;
    let level = logLevel;

    const { hasErrInstance, error } = LoggerUtils.isWithError(logData);

    switch (level) {
      case LogLevel.cache:
        level = LogLevel.info;
        break;
    }

    let objectToLog: object = {
      message,
      level,
      traceId: this.tracerUtils.getActiveTraceId(),
      service: this.serviceName,
      version: process.env.npm_package_version,
      env: this.appEnv,
      timestamp: Date.now(),
      ...LoggerUtils.stringifyLogData(
        hasErrInstance ? LoggerUtils.getErrorDetails(error) : logData
      )
    };

    if (this.clusterID) {
      objectToLog = { ...objectToLog, workerID: this.clusterID };
    }

    console.log(JSON.stringify(objectToLog));
  }
}

export const configureLogger = <T extends RepoEnv>(
  logger: typeof nodeColorLog,
  env: T,
  tracerUtils: TracerUtils,
  clusterID?: ClusterID
): LoggerType => {
  const isLocal = env.APP_ENV === ServiceEnvEnum.LOCAL;

  if (isLocal) {
    const localLogger = new WorkerLocalLogger(logger, clusterID);
    return (message, logData, logLevel = LogLevel.info) => {
      localLogger.executeDataLog({ message, logLevel, logData });
    };
  }

  const dataDogDevLogsEnabled = env.SEND_DEV_LOGS_TO_DATADOG === 'ENABLED';
  const dataDogCacheLogsEnabled = env.SEND_CACHE_LOGS_TO_DATADOG === 'ENABLED';
  const workerDataDogLogger = new WorkerDataDogLogger(
    env.SERVICE_NAME,
    env.APP_ENV,
    tracerUtils,
    clusterID
  );

  return (message, logData?, logLevel = LogLevel.info) => {
    if (
      (logLevel !== LogLevel.dev && logLevel !== LogLevel.cache) ||
      (logLevel === LogLevel.dev && dataDogDevLogsEnabled) ||
      (logLevel === LogLevel.cache && dataDogCacheLogsEnabled)
    ) {
      workerDataDogLogger.executeDataLog({ message, logLevel, logData });
    }
  };
};
