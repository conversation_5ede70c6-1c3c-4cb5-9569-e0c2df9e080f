#!/bin/sh

# Post-Push Hook - Runs npm audit after successful push
# This script is triggered by the reference-transaction hook after a successful push

echo "Post-push hook triggered - running npm audit..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    printf "${BLUE}[POST-PUSH HOOK]${NC} %s\n" "$1"
}

print_success() {
    printf "${GREEN}[POST-PUSH HOOK]${NC} %s\n" "$1"
}

print_warning() {
    printf "${YELLOW}[POST-PUSH HOOK]${NC} %s\n" "$1"
}

print_error() {
    printf "${RED}[POST-PUSH HOOK]${NC} %s\n" "$1"
}

print_status "Post-push hook triggered - running npm audit..."

# Check if package.json exists (indicating this is a Node.js project)
if [ ! -f "package.json" ]; then
    print_warning "No package.json found. Skipping npm audit."
    exit 0
fi

# Check if npm is available
if ! command -v npm >/dev/null 2>&1; then
    print_warning "npm not found. Skipping npm audit."
    exit 0
fi

# Run npm audit fix --dry-run --json to check for fixable vulnerabilities
print_status "Checking for fixable vulnerabilities..."

# Execute npm audit fix --dry-run --json and capture output
FIXABLE_AUDIT_REPORT_JSON=$(npm audit --dry-run --json 2>/dev/null)
AUDIT_EXIT_CODE=$?

# Check if npm audit fix --dry-run command itself failed
if [ $AUDIT_EXIT_CODE -ne 0 ]; then
    print_warning "npm audit fix --dry-run command failed. Running basic npm audit instead..."

    # Fallback to basic npm audit
    npm audit
    BASIC_AUDIT_EXIT_CODE=$?

    if [ $BASIC_AUDIT_EXIT_CODE -eq 0 ]; then
        print_success "No vulnerabilities found in packages!"
    else
        print_warning "npm audit found potential security issues."
        print_warning "Consider running 'npm audit fix' to address them."
    fi
    exit 0
fi

# Check if jq is available for JSON parsing
if command -v jq >/dev/null 2>&1; then
    # Use jq to check if there are any fixable actions in the dry-run report
    FIXABLE_COUNT=$(echo "$FIXABLE_AUDIT_REPORT_JSON" | jq '.actions | length' 2>/dev/null || echo "0")
else
    # Fallback: simple grep check for actions (less reliable but works without jq)
    if echo "$FIXABLE_AUDIT_REPORT_JSON" | grep -q '"action":'; then
        FIXABLE_COUNT=1
    else
        FIXABLE_COUNT=0
    fi
fi

# Display results based on fixable vulnerabilities found
if [ "$FIXABLE_COUNT" -gt 0 ]; then
    echo ""
    echo "################################################################################"
    echo "#                           SECURITY ALERT                                    #"
    echo "################################################################################"
    echo "# npm audit found fixable vulnerabilities in your project dependencies.       #"
    echo "#                                                                              #"
    echo "# Please run 'npm audit fix' locally to resolve these issues.                 #"
    echo "#                                                                              #"
    echo "# Review the changes carefully before committing, especially for major updates#"
    echo "################################################################################"
    echo ""

    # Optionally show a summary of vulnerabilities
    if command -v jq >/dev/null 2>&1; then
        print_status "Summary of fixable actions:"
        echo "$FIXABLE_AUDIT_REPORT_JSON" | jq -r '.actions[] | "  - \(.action): \(.module)"' 2>/dev/null || echo "  (Unable to parse action details)"
    fi
else
    # Run basic npm audit to show any vulnerabilities (even if not fixable)
    BASIC_AUDIT_OUTPUT=$(npm audit 2>/dev/null)
    BASIC_AUDIT_EXIT_CODE=$?

    if [ $BASIC_AUDIT_EXIT_CODE -eq 0 ]; then
        print_success "No vulnerabilities found in packages!"
    else
        print_warning "npm audit found some vulnerabilities, but none are automatically fixable."
        print_status "Run 'npm audit' for more details."
    fi
fi

print_status "Post-push hook completed."
