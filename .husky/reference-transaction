#!/bin/sh

# Reference Transaction Hook - Detects when remote tracking branches are updated after push
# This implements a true post-push hook by monitoring reference transactions
# Based on: https://stackoverflow.com/a/71176054

# Read the reference transaction data
while read oldvalue newvalue refname
echo "$oldvalue $newvalue $refname"
do
    # Check if this is a committed transaction on a remote tracking branch
    if [ "$1" = "committed" ] && echo "$refname" | grep -q "^refs/remotes/.*/.*"
    echo "$refname"
    echo "$refname" | grep -q "^refs/remotes/.*/.*"
    then
        # Extract remote and branch from refname (e.g., refs/remotes/origin/main)
        remote_and_branch=${refname#refs/remotes/}
        remote=${remote_and_branch%%/*}
        branch=${remote_and_branch#*/}

        echo "remote_and_branch:" "$remote_and_branch"
        echo "remote:" "$remote"
        echo "branch:" "$branch"
        
        # Only trigger for pushes to origin (you can modify this condition as needed)
        if [ "$remote" = "origin" ]
        then
            echo "Inside if remote = origin"
            # Execute the post-push script
            if [ -f ".husky/post-push" ]
            then
                echo "Executing post-push script"
                sh .husky/post-push "$remote" "$branch" "$oldvalue" "$newvalue"
            fi
            echo "Post-push script executed"
        fi
        echo "Outside if remote = origin"
    fi
    echo "Outside if transaction is committed"
done
