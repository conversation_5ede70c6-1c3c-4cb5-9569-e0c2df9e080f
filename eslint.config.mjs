import prettier from 'eslint-plugin-prettier';
import typescriptEslintEslintPlugin from '@typescript-eslint/eslint-plugin';
import globals from 'globals';
import tsParser from '@typescript-eslint/parser';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import js from '@eslint/js';
import { FlatCompat } from '@eslint/eslintrc';
// import importPlugin from 'eslint-plugin-import';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all
});

export default [
  ...compat.extends(
    'eslint:recommended',
    'plugin:@typescript-eslint/eslint-recommended',
    'plugin:@typescript-eslint/recommended',
    'prettier'
  ),
  {
    plugins: {
      prettier,
      '@typescript-eslint': typescriptEslintEslintPlugin
      // importPlugin
    },

    languageOptions: {
      globals: {
        ...globals.node,
        ...globals.jest
      },

      parser: tsParser,
      ecmaVersion: 2022,
      sourceType: 'module',

      parserOptions: {
        project: './tsconfig.json'
      }
    },

    rules: {
      camelcase: 'off',
      'consistent-return': 'off',
      'no-console': 'off',
      'no-plusplus': 'off',
      'no-unused-vars': 'off',
      'operator-linebreak': 'off',
      'function-paren-newline': 'off',
      'implicit-arrow-linebreak': 'off',
      'no-promise-executor-return': 'off',
      'no-param-reassign': 'off',
      'guard-for-in': 'off',
      'no-shadow': 'off',
      'no-nested-ternary': 'off',
      radix: 'off',
      'default-case': 'off',
      'no-continue': 'off',
      'no-redeclare': 'off',
      'no-await-in-loop': 'off',
      'no-return-assign': 'off',
      'class-methods-use-this': 'off',
      'linebreak-style': 'off',
      'comma-dangle': 'off',
      'arrow-body-style': 'off',
      'no-extra-boolean-cast': 'off',
      'eol-last': 'warn',
      // 'max-len': 'warn',
      eqeqeq: 'warn',
      'no-useless-escape': 'warn',
      'no-use-before-define': 'off',
      'no-unsafe-optional-chaining': 'warn',
      'no-useless-constructor': 'off',
      'no-return-await': 'off',
      'default-param-last': 'off',
      'no-empty-function': 'off',
      'no-restricted-syntax': 'off',
      'no-underscore-dangle': 'off',
      // 'importPlugin/prefer-default-export': 'off',
      // 'importPlugin/extensions': 'off',
      // 'importPlugin/no-unresolved': 'off',
      // 'importPlugin/no-mutable-exports': 'off',
      // 'importPlugin/no-extraneous-dependencies': 'off',
      // 'importPlugin/newline-after-import': 'warn',
      // 'importPlugin/no-duplicates': 'warn',
      // 'importPlugin/order': 'warn',
      // 'importPlugin/first': 'off',
      'prefer-promise-reject-errors': 'warn',
      'no-duplicate-imports': 'warn',
      'prefer-const': 'warn',
      'object-shorthand': 'warn',
      'max-classes-per-file': 'off',
      '@typescript-eslint/indent': 'off',
      '@typescript-eslint/interface-name-prefix': 'off',
      '@typescript-eslint/no-non-null-assertion': 'off',
      '@typescript-eslint/no-non-null-asserted-optional-chain': 'off',
      '@typescript-eslint/no-inferrable-types': 'off',
      '@typescript-eslint/no-var-requires': 'warn',
      '@typescript-eslint/no-empty-function': 'warn',
      '@typescript-eslint/explicit-module-boundary-types': 'warn',
      '@typescript-eslint/no-unused-vars': 'warn',
      '@typescript-eslint/no-shadow': 'warn',
      '@typescript-eslint/no-useless-constructor': 'warn',
      '@typescript-eslint/default-param-last': 'warn',
      '@typescript-eslint/no-explicit-any': 'off',

      'prettier/prettier': [
        'warn',
        {
          endOfLine: 'auto'
        }
      ]
    },
    // settings: {
    //   'importPlugin/parsers': {
    //     '@typescript-eslint/parser': ['.ts']
    //   },
    //   'importPlugin/resolver': {
    //     typescript: {
    //       alwaysTryTypes: true,
    //       project: './tsconfig.json'
    //     }
    //   }
    // },
    ignores: ['index.ts', 'eslint.config.mjs']
  }
];
