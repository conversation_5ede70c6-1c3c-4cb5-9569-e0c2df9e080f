#!/bin/bash

# Post-Push Hook - Runs npm audit after successful push
# This script is triggered by the reference-transaction hook after a successful push

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[POST-PUSH HOOK]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[POST-PUSH HOOK]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[POST-PUSH HOOK]${NC} $1"
}

print_error() {
    echo -e "${RED}[POST-PUSH HOOK]${NC} $1"
}

# Get the repository root directory
REPO_ROOT=$(git rev-parse --show-toplevel)

# Change to repository root to ensure we're in the right location
cd "$REPO_ROOT"

print_status "Post-push hook triggered - running npm audit..."

# Check if package.json exists (indicating this is a Node.js project)
if [ ! -f "package.json" ]; then
    print_warning "No package.json found. Skipping npm audit."
    exit 0
fi

# Check if npm is available
if ! command -v npm &> /dev/null; then
    print_warning "npm not found. Skipping npm audit."
    exit 0
fi

# Run npm audit
echo ""
echo "==================== NPM AUDIT RESULTS ===================="

# Run npm audit and capture its exit code, but don't fail the hook if audit finds issues
set +e
npm audit
AUDIT_EXIT_CODE=$?
set -e

echo "============================================================="

if [ $AUDIT_EXIT_CODE -eq 0 ]; then
    print_success "No vulnerabilities found in packages!"
else
    print_warning "npm audit found potential security issues."
    print_warning "Consider running 'npm audit fix' to address them."
    print_status "Run 'npm audit --help' for more information about available options."
fi

print_status "Post-push hook completed."
