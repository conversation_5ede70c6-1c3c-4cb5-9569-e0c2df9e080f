{"name": "ads-layouts-tools", "version": "1.6.0", "description": "common AdsLayouts tools", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"prepare": "husky && ln -sf ../../.husky/reference-transaction .git/hooks/reference-transaction", "prebuild": "rimraf -rf dist", "type-check": "tsc --noEmit", "type-check:watch": "npm run type-check -- --watch", "build:types": "tsc --emitDeclarationOnly", "build": "nest build", "check-types": "tsc --noEmit", "prettier": "prettier --check \"{src,libs}/**/*.ts\"", "prettier:fix": "prettier --write \"{src,libs}/**/*.ts\"", "lint": "eslint \"{src,libs}/**/*.ts\" --max-warnings=0", "lint:fix": "eslint \"{src,libs}/**/*.ts\" --fix", "lint-test": "run-s check-types prettier lint test", "test": "jest", "test:watch": "jest --watch", "coverage:local": "jest --coverage --coverageDirectory=../coverage-local --coverageReporters=lcov", "coverage": "jest --testResultsProcessor ../node_modules/jest-junit --coverage --coverageDirectory=../output/coverage", "bump": "npm version minor"}, "dependencies": {"@nestjs/common": "^10.4.15", "@nestjs/mongoose": "^10.1.0", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "envalid": "^8.0.0", "fastify": "^5.1.0", "json-rules-engine": "^7.3.1", "lodash": "^4.17.21", "mongoose": ">= 7.8.6 < 8", "node-fetch": "^2.7.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.17.0", "@nestjs/cli": "^10.4.9", "@nestjs/core": "^10.4.15", "@nestjs/testing": "^10.4.19", "@types/eslint__eslintrc": "^2.1.2", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.15", "@types/node-fetch": "^2.6.12", "@typescript-eslint/eslint-plugin": "^8.19.0", "@typescript-eslint/parser": "^8.32.0", "dd-trace": "^5.31.0", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "globals": "^15.14.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-junit": "^16.0.0", "lint-staged": "^15.3.0", "node-color-log": "^12.0.1", "prettier": "^3.4.2", "rimraf": "^6.0.1", "ts-jest": "^29.2.5", "typescript": "^5.7.2"}, "overrides": {"mongoose": "$mongoose"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coveragePathIgnorePatterns": ["libs/tools/src/schemas", "libs/tools/src/types", "libs/tools/src/index.ts"], "testEnvironment": "node", "coverageDirectory": "./coverage", "roots": ["<rootDir>/libs/"], "moduleNameMapper": {"^@adsLayouts/display-config-filters(|/.*)$": "<rootDir>/libs/display-config-filters/src/$1", "^@adsLayouts/tools(|/.*)$": "<rootDir>/libs/tools/src/$1"}}, "jest-junit": {"suiteName": "jest tests", "outputDirectory": "output/coverage", "outputName": "junit.xml", "classNameTemplate": "{classname} - {title}", "titleTemplate": "{classname} - {title}", "ancestorSeparator": " > ", "usePathForSuiteName": "true", "coverageReporters": ["text", "cobertura"]}, "gitHooks": {"pre-commit": "npx lint-staged"}, "lint-staged": {"*.{ts,tsx}": ["eslint \"{src,libs}/**/*.ts\" --fix", "prettier \"{src,libs}/**/*.ts\" --write"]}, "author": "Woj<PERSON><PERSON> <<EMAIL>>", "license": "ISC", "repository": {"type": "git", "url": "git+ssh://**************/discoveryinc-tvn/TVN-ad_tech-ads_layouts_tools.git"}, "files": ["dist", "README.md"], "engines": {"node": ">=22.0.0"}}